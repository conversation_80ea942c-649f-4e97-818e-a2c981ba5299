import { format, subDays } from 'date-fns';

// Christian names for mock data
const christianNames = {
    male: [
        'مايكل جورج صبحي',
        'مينا صموئيل فهيم',
        'كيرلس أنطونيوس نصيف',
        'مارك بولس عبدالله',
        'أندرو يوسف حنا',
        'جون مرقس إبراهيم',
        'بيتر إبراهيم يوسف',
        'ديفيد متى جرجس',
        'دانيال لوقا عزيز',
        'صموئيل يعقوب ميخائيل',
        'إسحق موسى فايز',
        'يوسف هارون نبيل',
        'متى فيليب سمير',
        'لوقا توما عادل',
        'مرقس برثولماوس رامي',
        'أنطونيوس سمعان وديع',
        'جرجس اسطفانوس مجدي',
        'كيرلس باسيليوس عماد',
        'أثناسيوس غريغوريوس شريف',
        'يوحنا خريستوستوم كريم',
        'مكاريوس أنبا بولا فادي',
        'شنودة الأرشيدياكون طارق',
        'بيشوي أنبا أنطونيوس هاني',
        'صرابامون أنبا بيشوي ماهر',
        'موسى الأسود عصام',
        'باخوميوس أنبا شنودة أيمن',
        'إفرام السرياني جمال',
        'يوأنس كاسيان خالد',
        'مكسيموس المعترف وائل',
        'يوحنا السلمي باسم',
        'أنطونيوس الكبير فؤاد',
        'بولا الطيبي رفيق',
        'مقار الكبير سامح',
        'أرسانيوس معلم أولاد الملوك علاء',
        'بيمن الكبير محمد',
        'سيصوي الكبير أحمد',
        'نيلوس السينائي حسام',
        'يوحنا القصير إيهاب',
        'أمونيوس الأسقف مصطفى',
        'بفنوتيوس الراهب ياسر',
    ],
    female: [
        'مريم يوسف عبدالله',
        'مارثا لعازر جرجس',
        'مريم المجدلية حنا',
        'سوسنة دانيال فهيم',
        'راعوث نعمي صبحي',
        'أستير مردخاي نصيف',
        'يهوديت هولوفرنيس عزيز',
        'حنة صموئيل ميخائيل',
        'أليصابات زكريا فايز',
        'سارة إبراهيم نبيل',
        'رفقة إسحق سمير',
        'راحيل يعقوب عادل',
        'ليئة يعقوب رامي',
        'دينة يعقوب وديع',
        'تامار يهوذا مجدي',
        'ميريام موسى عماد',
        'دبورة باراق شريف',
        'يائيل سيسرا كريم',
        'راعوث بوعز فادي',
        'حنة ألقانة طارق',
        'أبيجايل داود هاني',
        'بثشبع داود ماهر',
        'الملكة أستير عصام',
        'يهوديت بيت فلوى أيمن',
        'سوسنة البابلية جمال',
        'القديسة مريم العذراء خالد',
        'القديسة فيرينا وائل',
        'القديسة كاترين باسم',
        'القديسة بربارة فؤاد',
        'القديسة مارينا رفيق',
        'القديسة دميانة سامح',
        'القديسة رفقة علاء',
        'القديسة يوليانة محمد',
        'القديسة أوجينيا أحمد',
        'القديسة تكلا حسام',
        'القديسة سوسنة إيهاب',
        'القديسة أنسطاسيا مصطفى',
        'القديسة أغاثا ياسر',
        'القديسة لوسيا منير',
        'القديسة سيسيليا عبير',
    ],
};

const colleges = [
    'كلية الطب',
    'كلية الهندسة',
    'كلية الصيدلة',
    'كلية طب الأسنان',
    'كلية العلوم',
    'كلية التجارة',
    'كلية الحقوق',
    'كلية الآداب',
    'كلية التربية',
    'كلية الزراعة',
    'كلية الطب البيطري',
    'كلية الحاسبات والمعلومات',
    'كلية الفنون الجميلة',
    'كلية التربية الرياضية',
    'كلية الإعلام',
    'كلية السياحة والفنادق',
    'كلية التمريض',
    'كلية العلاج الطبيعي',
    'كلية الألسن',
    'كلية الاقتصاد والعلوم السياسية',
];

const departments = {
    'كلية الطب': ['طب عام', 'جراحة', 'باطنة', 'أطفال', 'نساء وتوليد', 'عيون', 'أنف وأذن', 'جلدية', 'نفسية'],
    'كلية الهندسة': ['مدني', 'معماري', 'ميكانيكا', 'كهرباء', 'حاسبات', 'بترول', 'كيميائية', 'طيران'],
    'كلية الصيدلة': ['صيدلة إكلينيكية', 'كيمياء صيدلية', 'عقاقير', 'صيدلانيات', 'علم الأدوية'],
    'كلية طب الأسنان': ['جراحة الفم', 'تقويم الأسنان', 'طب أسنان الأطفال', 'استعاضة', 'علاج الجذور'],
    'كلية العلوم': ['رياضيات', 'فيزياء', 'كيمياء', 'أحياء', 'جيولوجيا', 'حاسب آلي', 'إحصاء'],
    'كلية التجارة': ['محاسبة', 'إدارة أعمال', 'اقتصاد', 'إحصاء', 'نظم معلومات', 'تأمين'],
    'كلية الحقوق': ['قانون عام', 'قانون خاص', 'قانون دولي', 'شريعة إسلامية', 'قانون جنائي'],
    'كلية الآداب': ['لغة عربية', 'لغة إنجليزية', 'تاريخ', 'جغرافيا', 'فلسفة', 'علم نفس', 'اجتماع'],
    'كلية التربية': ['تربية ابتدائي', 'تربية إعدادي', 'تربية ثانوي', 'تربية خاصة', 'رياض أطفال'],
    'كلية الزراعة': ['إنتاج نباتي', 'إنتاج حيواني', 'علوم الأراضي', 'اقتصاد زراعي', 'هندسة زراعية'],
    'كلية الطب البيطري': ['طب باطني', 'جراحة', 'أمراض معدية', 'صحة عامة', 'تشريح'],
    'كلية الحاسبات والمعلومات': ['علوم حاسب', 'نظم معلومات', 'تكنولوجيا معلومات', 'ذكاء اصطناعي'],
    'كلية الفنون الجميلة': ['رسم وتصوير', 'نحت', 'جرافيك', 'ديكور', 'خزف'],
    'كلية التربية الرياضية': ['تدريب رياضي', 'إدارة رياضية', 'ترويح', 'تأهيل حركي'],
    'كلية الإعلام': ['صحافة', 'إذاعة وتلفزيون', 'علاقات عامة', 'إعلان'],
    'كلية السياحة والفنادق': ['سياحة', 'فنادق', 'إرشاد سياحي'],
    'كلية التمريض': ['تمريض عام', 'تمريض باطني', 'تمريض جراحي', 'تمريض أطفال'],
    'كلية العلاج الطبيعي': ['علاج طبيعي عام', 'علاج طبيعي للأطفال', 'علاج طبيعي رياضي'],
    'كلية الألسن': ['لغة إنجليزية', 'لغة فرنسية', 'لغة ألمانية', 'لغة إيطالية', 'لغة صينية'],
    'كلية الاقتصاد والعلوم السياسية': ['اقتصاد', 'علوم سياسية', 'إحصاء', 'دبلوماسية'],
};

const addresses = [
    'شارع الجمهورية، وسط البلد، القاهرة',
    'شارع فيصل، الهرم، الجيزة',
    'مدينة نصر، القاهرة الجديدة',
    'المعادي، القاهرة',
    'الزمالك، القاهرة',
    'مصر الجديدة، هليوبوليس',
    'شبرا الخيمة، القليوبية',
    'الإسكندرية، كورنيش النيل',
    'طنطا، الغربية',
    'المنصورة، الدقهلية',
    'أسيوط، صعيد مصر',
    'سوهاج، صعيد مصر',
    'قنا، صعيد مصر',
    'الأقصر، صعيد مصر',
    'أسوان، جنوب مصر',
    'بورسعيد، قناة السويس',
    'الإسماعيلية، قناة السويس',
    'السويس، البحر الأحمر',
    'الغردقة، البحر الأحمر',
    'شرم الشيخ، جنوب سيناء',
    'العريش، شمال سيناء',
    'دمياط، دلتا النيل',
    'كفر الشيخ، دلتا النيل',
    'بنها، القليوبية',
    'الزقازيق، الشرقية',
    'بني سويف، صعيد مصر',
    'الفيوم، صعيد مصر',
    'المنيا، صعيد مصر',
    'ملوي، المنيا',
    'أبو تيج، أسيوط',
];

export function generateMockUsers(count = 150) {
    const users = [];

    for (let i = 0; i < count; i++) {
        const gender = Math.random() > 0.5 ? 'male' : 'female';
        const names = christianNames[gender];
        const name = names[Math.floor(Math.random() * names.length)];
        const college = colleges[Math.floor(Math.random() * colleges.length)];
        const departmentList = departments[college] || ['قسم عام'];
        const department = departmentList[Math.floor(Math.random() * departmentList.length)];

        // Generate realistic phone number
        const phonePrefix = ['010', '011', '012', '015'][Math.floor(Math.random() * 4)];
        const phoneNumber =
            phonePrefix +
            Math.floor(Math.random() * 100000000)
                .toString()
                .padStart(8, '0');

        // Generate birthdate (18-25 years old)
        const age = 18 + Math.floor(Math.random() * 8);
        const birthYear = new Date().getFullYear() - age;
        const birthMonth = Math.floor(Math.random() * 12);
        const birthDay = Math.floor(Math.random() * 28) + 1;
        const birthdate = new Date(birthYear, birthMonth, birthDay);

        // Generate first attendance date (within last 2 years)
        const firstAttendanceDate = subDays(new Date(), Math.floor(Math.random() * 730));

        const user = {
            id: `user_${i + 1}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name,
            phone: phoneNumber,
            gender: gender as 'male' | 'female',
            year: (Math.floor(Math.random() * 4) + 1) as 1 | 2 | 3 | 4,
            college,
            department,
            birthdate: format(birthdate, 'yyyy-MM-dd'),
            address: addresses[Math.floor(Math.random() * addresses.length)],
            facebook_url: Math.random() > 0.3 ? `https://facebook.com/${name.replace(/\s+/g, '').toLowerCase()}` : '',
            first_attendance_date: format(firstAttendanceDate, 'yyyy-MM-dd'),
            qr_code: `QR_${i + 1}_${Date.now()}`,
            created_at: format(subDays(new Date(), Math.floor(Math.random() * 365)), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
            updated_at: format(subDays(new Date(), Math.floor(Math.random() * 30)), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        };

        users.push(user);
    }

    return users;
}

export function generateMockAttendance(users: any[], daysBack = 120) {
    const attendanceRecords = [];
    const today = new Date();

    for (let dayOffset = 0; dayOffset < daysBack; dayOffset++) {
        const date = subDays(today, dayOffset);
        const dateString = format(date, 'yyyy-MM-dd');

        // Skip some days randomly (no meetings) - weekends and some weekdays
        const dayOfWeek = date.getDay();
        if (dayOfWeek === 6 || dayOfWeek === 0 || Math.random() < 0.2) continue;

        // Randomly select users who attended (75-95% attendance rate)
        const attendanceRate = 0.75 + Math.random() * 0.2;
        const shuffledUsers = users.sort(() => Math.random() - 0.5);
        const attendingUsers = shuffledUsers.slice(0, Math.floor(users.length * attendanceRate));

        attendingUsers.forEach((user) => {
            const record = {
                id: `attendance_${dateString}_${user.id}_${Math.random().toString(36).substr(2, 9)}`,
                user_id: user.id,
                user_name: user.name,
                date: dateString,
                present: true,
                marked_by: '<EMAIL>',
                created_at: format(date, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
            };
            attendanceRecords.push(record);
        });

        // Add some absent records for tracking
        const absentUsers = shuffledUsers.filter((user) => !attendingUsers.includes(user));
        absentUsers.slice(0, Math.floor(absentUsers.length * 0.2)).forEach((user) => {
            const record = {
                id: `attendance_absent_${dateString}_${user.id}_${Math.random().toString(36).substr(2, 9)}`,
                user_id: user.id,
                user_name: user.name,
                date: dateString,
                present: false,
                marked_by: 'system',
                created_at: format(date, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
            };
            attendanceRecords.push(record);
        });
    }

    return attendanceRecords;
}

export function initializeMockData() {
    const users = generateMockUsers(150);
    const attendanceRecords = generateMockAttendance(users, 120);

    return {
        users,
        attendanceRecords,
    };
}
