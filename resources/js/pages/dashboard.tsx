import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useRTL } from '@/contexts/rtl-context';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/stores/app-store';
import { Link } from '@inertiajs/react';
import {
    Activity,
    AlertTriangle,
    ArrowLeft,
    ArrowRight,
    Award,
    BarChart3,
    Clock,
    Download,
    Eye,
    Gift,
    Sparkles,
    Target,
    TrendingUp,
    UserCheck,
    UserPlus,
    Users,
    UserX,
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface DashboardStats {
    totalUsers: number;
    presentToday: number;
    absentToday: number;
    consistentAttendees: number;
    redFlags: number;
    attendanceRate: number;
    weeklyAttendance: number;
    monthlyAttendance: number;
}

function DashboardPage() {
    const { isRTL, direction } = useRTL();
    const { users, attendanceRecords } = useAppStore();
    const [stats, setStats] = useState<DashboardStats>({
        totalUsers: 0,
        presentToday: 0,
        absentToday: 0,
        consistentAttendees: 0,
        redFlags: 0,
        attendanceRate: 0,
        weeklyAttendance: 0,
        monthlyAttendance: 0,
    });
    const [loading, setLoading] = useState(true);
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
        calculateDashboardStats();
    }, [users, attendanceRecords]);

    const calculateDashboardStats = async () => {
        try {
            // Simulate API delay for better UX
            await new Promise((resolve) => setTimeout(resolve, 500));

            const today = new Date().toISOString().split('T')[0];
            const weekStart = new Date();
            weekStart.setDate(weekStart.getDate() - 7);
            const monthStart = new Date();
            monthStart.setDate(monthStart.getDate() - 30);

            // Today's attendance
            const todayRecords = attendanceRecords.filter((r) => r.date === today);
            const presentToday = todayRecords.filter((r) => r.present).length;
            const absentToday = users.length - presentToday;

            // Weekly attendance
            const weekRecords = attendanceRecords.filter((r) => {
                const recordDate = new Date(r.date);
                return recordDate >= weekStart && recordDate <= new Date();
            });
            const weeklyPresent = weekRecords.filter((r) => r.present).length;
            const weeklyTotal = weekRecords.length;
            const weeklyAttendance = weeklyTotal > 0 ? Math.round((weeklyPresent / weeklyTotal) * 100) : 0;

            // Monthly attendance
            const monthRecords = attendanceRecords.filter((r) => {
                const recordDate = new Date(r.date);
                return recordDate >= monthStart && recordDate <= new Date();
            });
            const monthlyPresent = monthRecords.filter((r) => r.present).length;
            const monthlyTotal = monthRecords.length;
            const monthlyAttendance = monthlyTotal > 0 ? Math.round((monthlyPresent / monthlyTotal) * 100) : 0;

            // Consistent attendees (80%+ attendance rate)
            const userAttendanceRates = users.map((user) => {
                const userRecords = attendanceRecords.filter((r) => r.user_id === user.id);
                const userPresent = userRecords.filter((r) => r.present).length;
                const userTotal = userRecords.length;
                return {
                    userId: user.id,
                    rate: userTotal > 0 ? (userPresent / userTotal) * 100 : 0,
                };
            });

            const consistentAttendees = userAttendanceRates.filter((u) => u.rate >= 80).length;
            const redFlags = userAttendanceRates.filter((u) => u.rate < 50 && u.rate > 0).length;

            const attendanceRate = users.length > 0 ? Math.round((presentToday / users.length) * 100) : 0;

            setStats({
                totalUsers: users.length,
                presentToday,
                absentToday,
                consistentAttendees,
                redFlags,
                attendanceRate,
                weeklyAttendance,
                monthlyAttendance,
            });
        } catch (error) {
            console.error('Error calculating dashboard stats:', error);
        } finally {
            setLoading(false);
        }
    };

    const statCards = [
        {
            title: 'إجمالي الأعضاء',
            value: stats.totalUsers,
            icon: Users,
            color: 'text-blue-600',
            bgColor: 'bg-blue-100',
            gradient: 'from-blue-500 to-blue-600',
            change: '+12 هذا الشهر',
            changeType: 'positive' as const,
        },
        {
            title: 'الحاضرون اليوم',
            value: stats.presentToday,
            icon: UserCheck,
            color: 'text-green-600',
            bgColor: 'bg-green-100',
            gradient: 'from-green-500 to-green-600',
            change: `${stats.attendanceRate}% معدل الحضور`,
            changeType: 'positive' as const,
        },
        {
            title: 'الغائبون اليوم',
            value: stats.absentToday,
            icon: UserX,
            color: 'text-red-600',
            bgColor: 'bg-red-100',
            gradient: 'from-red-500 to-red-600',
            change: '-3 من الأسبوع الماضي',
            changeType: 'positive' as const,
        },
        {
            title: 'الحضور المنتظم',
            value: stats.consistentAttendees,
            icon: Award,
            color: 'text-emerald-600',
            bgColor: 'bg-emerald-100',
            gradient: 'from-emerald-500 to-emerald-600',
            change: '80%+ معدل حضور',
            changeType: 'positive' as const,
        },
        {
            title: 'يحتاج متابعة',
            value: stats.redFlags,
            icon: AlertTriangle,
            color: 'text-orange-600',
            bgColor: 'bg-orange-100',
            gradient: 'from-orange-500 to-orange-600',
            change: 'أقل من 50% حضور',
            changeType: 'neutral' as const,
        },
        {
            title: 'المعدل الأسبوعي',
            value: `${stats.weeklyAttendance}%`,
            icon: TrendingUp,
            color: 'text-purple-600',
            bgColor: 'bg-purple-100',
            gradient: 'from-purple-500 to-purple-600',
            change: 'آخر 7 أيام',
            changeType: 'positive' as const,
        },
    ];

    const quickActions = [
        {
            title: 'تسجيل الحضور',
            description: 'تسجيل حضور الأعضاء للاجتماع',
            icon: UserCheck,
            href: '/attendance',
            color: 'from-blue-500 to-blue-600',
            iconBg: 'bg-blue-100',
            iconColor: 'text-blue-600',
        },
        {
            title: 'إضافة عضو جديد',
            description: 'إضافة عضو جديد للنظام',
            icon: UserPlus,
            href: '/add-user',
            color: 'from-green-500 to-green-600',
            iconBg: 'bg-green-100',
            iconColor: 'text-green-600',
        },
        {
            title: 'عرض التقارير',
            description: 'تقارير مفصلة عن الحضور',
            icon: BarChart3,
            href: '/reports',
            color: 'from-purple-500 to-purple-600',
            iconBg: 'bg-purple-100',
            iconColor: 'text-purple-600',
        },
        {
            title: 'أعياد الميلاد',
            description: 'إدارة أعياد ميلاد الأعضاء',
            icon: Gift,
            href: '/birthdays',
            color: 'from-pink-500 to-pink-600',
            iconBg: 'bg-pink-100',
            iconColor: 'text-pink-600',
        },
    ];

    const ArrowIcon = isRTL ? ArrowLeft : ArrowRight;

    if (!mounted) return null;

    return (
        <div className="page-transition font-cairo space-y-8 p-6" dir={direction}>
            {/* Header */}
            <div className="animate-fade-in">
                <div className={cn('flex items-center justify-between','flex-row')}>
                    <div>
                        <div className={cn('mb-2 flex items-center gap-3',  'flex-row')}>
                            <div className="rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 p-2">
                                <Target className="h-6 w-6 text-white" />
                            </div>
                            <h1 className="py-1 gradient-text text-4xl font-bold">لوحة التحكم</h1>
                            <Sparkles className="h-6 w-6 animate-pulse text-yellow-500" />
                        </div>
                        <p className={cn('text-body text-lg text-gray-600')}>
                            نظرة عامة شاملة على نظام حضور الشباب
                        </p>
                        <div className={cn('mt-2 flex items-center gap-2', 'flex-row')}>
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-500">آخر تحديث: {new Date().toLocaleString('ar-EG')}</span>
                        </div>
                    </div>

                    <div className="flex gap-2">
                        <Button variant="outline">
                            <Download className="mr-2 h-4 w-4" />
                            تصدير التقرير
                        </Button>
                        <Button variant="outline">
                            <Eye className="mr-2 h-4 w-4" />
                            عرض مفصل
                        </Button>
                    </div>
                </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {statCards.map((card, index) => (
                    <Card
                        key={index}
                        className="hover-lift shadow-soft animate-scale-in group relative overflow-hidden border-0 bg-white/80 backdrop-blur-sm"
                        style={{ animationDelay: `${index * 0.1}s` }}
                    >
                        {/* Background Gradient */}
                        <div className={`absolute inset-0 bg-gradient-to-br ${card.gradient} opacity-5 transition-opacity group-hover:opacity-10`} />

                        <CardHeader
                            className={cn('relative flex items-center justify-between space-y-0 pb-3', isRTL ? 'flex-row-reverse' : 'flex-row')}
                        >
                            <CardTitle className={cn('font-cairo text-sm font-medium text-gray-600', isRTL ? 'text-right' : 'text-left')}>
                                {card.title}
                            </CardTitle>
                            <div className={`rounded-lg p-2 ${card.bgColor} hover-scale transition-transform group-hover:scale-110`}>
                                <card.icon className={`h-5 w-5 ${card.color}`} />
                            </div>
                        </CardHeader>
                        <CardContent className="relative">
                            <div className={cn('flex items-baseline gap-2', isRTL ? 'flex-row-reverse' : 'flex-row')}>
                                <div className="text-3xl font-bold text-gray-900">
                                    {loading ? <div className="loading-shimmer h-8 w-12 rounded bg-gray-200"></div> : card.value}
                                </div>
                                {!loading && (
                                    <div
                                        className={cn(
                                            'rounded-full px-2 py-1 text-xs',
                                            card.changeType === 'positive' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700',
                                        )}
                                    >
                                        {card.change}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-4">
                {quickActions.map((action, index) => (
                    <Link key={index} href={action.href}>
                        <Card
                            className="hover-lift shadow-soft animate-slide-in-right group relative h-full cursor-pointer overflow-hidden border-0 bg-white/80 backdrop-blur-sm"
                            style={{ animationDelay: `${index * 0.1 + 0.5}s` }}
                        >
                            {/* Background Gradient on Hover */}
                            <div
                                className={`absolute inset-0 bg-gradient-to-br ${action.color} opacity-0 transition-opacity duration-300 group-hover:opacity-10`}
                            />

                            <CardHeader className="relative">
                                <div className={cn('flex items-center justify-between', isRTL ? 'flex-row-reverse' : 'flex-row')}>
                                    <div className={`rounded-xl p-3 ${action.iconBg} transition-transform duration-300 group-hover:scale-110`}>
                                        <action.icon className={`h-6 w-6 ${action.iconColor}`} />
                                    </div>
                                    <ArrowIcon className="h-5 w-5 text-gray-400 transition-all duration-300 group-hover:translate-x-1 group-hover:text-gray-600" />
                                </div>
                                <CardTitle
                                    className={cn(
                                        'text-lg font-bold text-gray-900 transition-colors group-hover:text-gray-700',
                                        isRTL ? 'text-right' : 'text-left',
                                    )}
                                >
                                    {action.title}
                                </CardTitle>
                                <CardDescription className={cn('text-body text-gray-600', isRTL ? 'text-right' : 'text-left')}>
                                    {action.description}
                                </CardDescription>
                            </CardHeader>
                        </Card>
                    </Link>
                ))}
            </div>

            {/* Additional Info Cards */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <Card className="hover-lift shadow-soft animate-fade-in border-0 bg-white/80 backdrop-blur-sm" style={{ animationDelay: '0.8s' }}>
                    <CardHeader>
                        <div className={cn('flex items-center gap-3', isRTL ? 'flex-row-reverse' : 'flex-row')}>
                            <div className="rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 p-2">
                                <Activity className="h-5 w-5 text-white" />
                            </div>
                            <CardTitle className={cn('font-cairo font-medium', isRTL ? 'text-right' : 'text-left')}>إحصائيات الأداء</CardTitle>
                        </div>
                        <CardDescription className={cn('text-body text-gray-600', isRTL ? 'text-right' : 'text-left')}>
                            معلومات مهمة عن أداء النظام هذا الأسبوع
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="hover-scale flex items-center justify-between rounded-lg bg-blue-50 p-3">
                                <span className={cn('font-cairo text-sm font-medium text-gray-700', isRTL ? 'text-right' : 'text-left')}>
                                    معدل الحضور الأسبوعي
                                </span>
                                <span className="font-bold text-blue-600">{stats.weeklyAttendance}%</span>
                            </div>
                            <div className="hover-scale flex items-center justify-between rounded-lg bg-green-50 p-3">
                                <span className={cn('font-cairo text-sm font-medium text-gray-700', isRTL ? 'text-right' : 'text-left')}>
                                    الحضور المنتظم
                                </span>
                                <span className="font-bold text-green-600">{stats.consistentAttendees} عضو</span>
                            </div>
                            <div className="hover-scale flex items-center justify-between rounded-lg bg-orange-50 p-3">
                                <span className={cn('font-cairo text-sm font-medium text-gray-700', isRTL ? 'text-right' : 'text-left')}>
                                    يحتاج متابعة
                                </span>
                                <span className="font-bold text-orange-600">{stats.redFlags} عضو</span>
                            </div>
                            <div className="hover-scale flex items-center justify-between rounded-lg bg-purple-50 p-3">
                                <span className={cn('font-cairo text-sm font-medium text-gray-700', isRTL ? 'text-right' : 'text-left')}>
                                    المعدل الشهري
                                </span>
                                <span className="font-bold text-purple-600">{stats.monthlyAttendance}%</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="hover-lift shadow-soft animate-fade-in border-0 bg-white/80 backdrop-blur-sm" style={{ animationDelay: '0.9s' }}>
                    <CardHeader>
                        <div className={cn('flex items-center gap-3', isRTL ? 'flex-row-reverse' : 'flex-row')}>
                            <div className="rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 p-2">
                                <Sparkles className="h-5 w-5 text-white" />
                            </div>
                            <CardTitle className={cn('font-cairo font-medium', isRTL ? 'text-right' : 'text-left')}>الميزات الجديدة</CardTitle>
                        </div>
                        <CardDescription className={cn('text-body text-gray-600', isRTL ? 'text-right' : 'text-left')}>
                            آخر التحديثات والميزات المضافة للنظام
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="hover-scale flex items-center gap-3 rounded-lg bg-gradient-to-r from-purple-50 to-pink-50 p-3">
                                <div className="h-2 w-2 flex-shrink-0 rounded-full bg-purple-500"></div>
                                <span className={cn('font-cairo text-sm font-medium text-gray-700', isRTL ? 'text-right' : 'text-left')}>
                                    تحسينات في واجهة المستخدم
                                </span>
                            </div>
                            <div className="hover-scale flex items-center gap-3 rounded-lg bg-gradient-to-r from-blue-50 to-cyan-50 p-3">
                                <div className="h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
                                <span className={cn('font-cairo text-sm font-medium text-gray-700', isRTL ? 'text-right' : 'text-left')}>
                                    إضافة نظام أعياد الميلاد
                                </span>
                            </div>
                            <div className="hover-scale flex items-center gap-3 rounded-lg bg-gradient-to-r from-green-50 to-emerald-50 p-3">
                                <div className="h-2 w-2 flex-shrink-0 rounded-full bg-green-500"></div>
                                <span className={cn('font-cairo text-sm font-medium text-gray-700', isRTL ? 'text-right' : 'text-left')}>
                                    تقارير محسنة مع الرسوم البيانية
                                </span>
                            </div>
                            <div className="hover-scale flex items-center gap-3 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 p-3">
                                <div className="h-2 w-2 flex-shrink-0 rounded-full bg-yellow-500"></div>
                                <span className={cn('font-cairo text-sm font-medium text-gray-700', isRTL ? 'text-right' : 'text-left')}>
                                    تصدير البيانات بصيغ متعددة
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}

DashboardPage.layout = (page: React.ReactNode) => <AppLayout children={page} />;

export default DashboardPage;
