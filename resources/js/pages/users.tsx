'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DataTable, SortableHeader } from '@/components/ui/data-table';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useRTL } from '@/contexts/rtl-context';
import { useUserSearch } from '@/hooks/use-search';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { useAppStore, type User as AppUser } from '@/stores/app-store';
import { type ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import {
    Calendar,
    CheckCircle,
    Download,
    Edit,
    Eye,
    Facebook,
    GraduationCap,
    MapPin,
    MoreHorizontal,
    Phone,
    Plus,
    QrCode,
    Trash2,
    Upload,
    UserCheck,
    Users,
    X,
} from 'lucide-react';
import { useMemo, useState } from 'react';

interface UsersPageProps {
    year?: 1 | 2 | 3 | 4;
}

function UsersPage({ year = 1 }: UsersPageProps) {
    const { users, deleteUser, setSelectedUser, exportUsers, bulkMarkAttendance } = useAppStore();
    const { isRTL, direction } = useRTL();

    const [showQRModal, setShowQRModal] = useState<AppUser | null>(null);
    const [showUserDetails, setShowUserDetails] = useState<AppUser | null>(null);
    const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

    // Filter users by year first
    const yearUsers = useMemo(() => users.filter((user) => user.year === year), [users, year]);

    // Initialize search with year-filtered users
    const {
        query,
        setQuery,
        results: searchResults,
        isSearching,
        suggestions,
        filters,
        setFilters,
        clearSearch,
        facets,
    } = useUserSearch(yearUsers, {
        debounceMs: 300,
        enableSuggestions: true,
        maxSuggestions: 5,
    });

    // Get the actual user data from search results
    const displayUsers = searchResults.map((result: any) => result.item);

    const columns: ColumnDef<AppUser>[] = [
        {
            id: 'select',
            header: ({ table }) => (
                <Checkbox
                    checked={table.getIsAllPageRowsSelected()}
                    onCheckedChange={(value) => {
                        table.toggleAllPageRowsSelected(!!value);
                        if (value) {
                            setSelectedUsers(table.getRowModel().rows.map((row) => row.original.id));
                        } else {
                            setSelectedUsers([]);
                        }
                    }}
                    aria-label="تحديد الكل"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={selectedUsers.includes(row.original.id)}
                    onCheckedChange={(value) => {
                        if (value) {
                            setSelectedUsers([...selectedUsers, row.original.id]);
                        } else {
                            setSelectedUsers(selectedUsers.filter((id) => id !== row.original.id));
                        }
                    }}
                    aria-label="تحديد الصف"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: 'name',
            header: ({ column }) => <SortableHeader column={column}>الاسم</SortableHeader>,
            cell: ({ row }) => (
                <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
                        <span className="text-sm font-bold text-white">{row.original.name.charAt(0)}</span>
                    </div>
                    <div>
                        <div className="font-medium">{row.getValue('name')}</div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                            <Phone className="h-3 w-3" />
                            {row.original.phone}
                        </div>
                    </div>
                </div>
            ),
        },
        {
            accessorKey: 'gender',
            header: 'النوع',
            cell: ({ row }) => (
                <Badge variant={row.getValue('gender') === 'male' ? 'default' : 'secondary'}>
                    {row.getValue('gender') === 'male' ? 'ذكر' : 'أنثى'}
                </Badge>
            ),
        },
        {
            accessorKey: 'college',
            header: 'الكلية',
            cell: ({ row }) => (
                <div className="flex items-center gap-2">
                    <GraduationCap className="h-4 w-4 text-gray-500" />
                    <div>
                        <div className="font-medium">{row.getValue('college')}</div>
                        <div className="text-sm text-gray-500">{row.original.department}</div>
                    </div>
                </div>
            ),
        },
        {
            accessorKey: 'birthdate',
            header: 'تاريخ الميلاد',
            cell: ({ row }) => (
                <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span>{format(new Date(row.getValue('birthdate')), 'dd/MM/yyyy', { locale: ar })}</span>
                </div>
            ),
        },
        {
            accessorKey: 'first_attendance_date',
            header: 'أول حضور',
            cell: ({ row }) => <span>{format(new Date(row.getValue('first_attendance_date')), 'dd/MM/yyyy', { locale: ar })}</span>,
        },
        {
            id: 'actions',
            header: 'الإجراءات',
            cell: ({ row }) => {
                const user = row.original;

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => setShowUserDetails(user)}>
                                <Eye className="mr-2 h-4 w-4" />
                                عرض التفاصيل
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setShowQRModal(user)}>
                                <QrCode className="mr-2 h-4 w-4" />
                                عرض QR Code
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setSelectedUser(user)}>
                                <Edit className="mr-2 h-4 w-4" />
                                تعديل
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => bulkMarkAttendance([user.id], true, '<EMAIL>')} className="text-green-600">
                                <UserCheck className="mr-2 h-4 w-4" />
                                تسجيل حضور
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => deleteUser(user.id)} className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                حذف
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    // Create filters component
    const filtersComponent = (
        <div className="flex items-center gap-2">
            <Select value={filters.gender || 'all'} onValueChange={(value) => setFilters({ ...filters, gender: value as any })}>
                <SelectTrigger className="w-32">
                    <SelectValue placeholder="النوع" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">الكل</SelectItem>
                    <SelectItem value="male">ذكر</SelectItem>
                    <SelectItem value="female">أنثى</SelectItem>
                </SelectContent>
            </Select>

            {facets && facets.colleges.length > 1 && (
                <Select value={filters.college || 'all'} onValueChange={(value) => setFilters({ ...filters, college: value })}>
                    <SelectTrigger className="w-40">
                        <SelectValue placeholder="الكلية" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">جميع الكليات</SelectItem>
                        {facets.colleges.map((college: any) => (
                            <SelectItem key={college.value} value={college.value}>
                                {college.value} ({college.count})
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            )}
        </div>
    );

    const yearNames = {
        1: 'السنة الأولى',
        2: 'السنة الثانية',
        3: 'السنة الثالثة',
        4: 'السنة الرابعة',
    };

    const handleExport = (format: 'csv' | 'excel' | 'pdf') => {
        exportUsers(format, { year });
    };

    const handleBulkAction = (action: string) => {
        if (action === 'mark_present') {
            bulkMarkAttendance(selectedUsers, true, '<EMAIL>');
        } else if (action === 'mark_absent') {
            bulkMarkAttendance(selectedUsers, false, '<EMAIL>');
        }
        setSelectedUsers([]);
    };

    return (
        <div className="page-transition font-cairo space-y-6 p-6" dir={direction}>
            {/* Header */}
            <div className="animate-fade-in">
                <div className={cn('flex items-center justify-between', 'flex-row')}>
                    <div>
                        <div className={cn('mb-2 flex items-center gap-3', 'flex-row')}>
                            <div className="rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 p-2">
                                <Users className="h-6 w-6 text-white" />
                            </div>
                            <h1 className="text-heading gradient-text text-4xl font-bold">{yearNames[year]}</h1>
                            <Badge variant="outline" className="px-3 py-1 text-lg">
                                {yearUsers.length} طالب
                            </Badge>
                        </div>
                        <p className={cn('text-body text-lg text-gray-600', isRTL ? 'text-right' : 'text-left')}>
                            إدارة طلاب {yearNames[year]} - عرض وتعديل بيانات الطلاب
                        </p>
                    </div>

                    <div className="flex gap-2">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline">
                                    <Download className="mr-2 h-4 w-4" />
                                    تصدير
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                                <DropdownMenuItem onClick={() => handleExport('csv')}>تصدير CSV</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleExport('excel')}>تصدير Excel</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleExport('pdf')}>تصدير PDF</DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>

                        <Button variant="outline">
                            <Upload className="mr-2 h-4 w-4" />
                            استيراد
                        </Button>

                        <Button className="btn-gradient">
                            <Plus className="mr-2 h-4 w-4" />
                            إضافة طالب جديد
                        </Button>
                    </div>
                </div>
            </div>

            {/* Bulk Actions */}
            {selectedUsers.length > 0 && (
                <Card className="animate-scale-in border-blue-200 bg-blue-50">
                    <CardContent className="flex items-center justify-between p-4">
                        <div className="flex items-center gap-3">
                            <CheckCircle className="h-5 w-5 text-blue-600" />
                            <span className="font-medium text-blue-800">تم تحديد {selectedUsers.length} طالب</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button size="sm" onClick={() => handleBulkAction('mark_present')} className="bg-green-600 hover:bg-green-700">
                                <UserCheck className="mr-2 h-4 w-4" />
                                تسجيل حضور
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleBulkAction('mark_absent')}>
                                تسجيل غياب
                            </Button>
                            <Button size="sm" variant="ghost" onClick={() => setSelectedUsers([])}>
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Enhanced Data Table with Search */}
            <DataTable
                columns={columns}
                data={displayUsers}
                searchPlaceholder="البحث في الأعضاء..."
                onSearch={setQuery}
                searchQuery={query}
                isSearching={isSearching}
                searchSuggestions={suggestions}
                onSuggestionSelect={setQuery}
                filters={filtersComponent}
                toolbar={
                    <div className="flex items-center gap-2">
                        {selectedUsers.length > 0 && (
                            <div className="flex items-center gap-2">
                                <Badge variant="secondary">{selectedUsers.length} محدد</Badge>
                                <Button size="sm" onClick={() => handleBulkAction('mark_present')}>
                                    تسجيل حضور
                                </Button>
                                <Button size="sm" variant="outline" onClick={() => handleBulkAction('mark_absent')}>
                                    تسجيل غياب
                                </Button>
                            </div>
                        )}
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm">
                                    <Download className="mr-2 h-4 w-4" />
                                    تصدير
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                                <DropdownMenuItem onClick={() => handleExport('csv')}>تصدير CSV</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleExport('excel')}>تصدير Excel</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleExport('pdf')}>تصدير PDF</DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                }
                isRTL={isRTL}
                pageSize={20}
            />

            {/* User Details Modal */}
            {showUserDetails && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                    <Card className="animate-scale-in max-h-[90vh] w-full max-w-2xl overflow-y-auto">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="flex items-center gap-2">
                                    <Eye className="h-5 w-5" />
                                    تفاصيل العضو - {showUserDetails.name}
                                </CardTitle>
                                <Button variant="ghost" size="icon" onClick={() => setShowUserDetails(null)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div className="space-y-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">الاسم الكامل</label>
                                        <p className="text-lg font-medium">{showUserDetails.name}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">رقم الهاتف</label>
                                        <p className="flex items-center gap-2">
                                            <Phone className="h-4 w-4" />
                                            {showUserDetails.phone}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">النوع</label>
                                        <p>
                                            <Badge variant={showUserDetails.gender === 'male' ? 'default' : 'secondary'}>
                                                {showUserDetails.gender === 'male' ? 'ذكر' : 'أنثى'}
                                            </Badge>
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">السنة الدراسية</label>
                                        <p className="text-lg font-medium">السنة {showUserDetails.year}</p>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">الكلية</label>
                                        <p className="flex items-center gap-2">
                                            <GraduationCap className="h-4 w-4" />
                                            {showUserDetails.college}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">القسم</label>
                                        <p>{showUserDetails.department}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">تاريخ الميلاد</label>
                                        <p className="flex items-center gap-2">
                                            <Calendar className="h-4 w-4" />
                                            {format(new Date(showUserDetails.birthdate), 'dd MMMM yyyy', { locale: ar })}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">أول حضور</label>
                                        <p>{format(new Date(showUserDetails.first_attendance_date), 'dd MMMM yyyy', { locale: ar })}</p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-500">العنوان</label>
                                <p className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4" />
                                    {showUserDetails.address}
                                </p>
                            </div>

                            {showUserDetails.facebook_url && (
                                <div>
                                    <label className="text-sm font-medium text-gray-500">الفيسبوك</label>
                                    <p className="flex items-center gap-2">
                                        <Facebook className="h-4 w-4" />
                                        <a
                                            href={showUserDetails.facebook_url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:underline"
                                        >
                                            {showUserDetails.facebook_url}
                                        </a>
                                    </p>
                                </div>
                            )}

                            <div className="flex gap-2 border-t pt-4">
                                <Button onClick={() => setShowQRModal(showUserDetails)} variant="outline" className="flex-1">
                                    <QrCode className="mr-2 h-4 w-4" />
                                    عرض QR Code
                                </Button>
                                <Button onClick={() => setSelectedUser(showUserDetails)} className="btn-gradient flex-1">
                                    <Edit className="mr-2 h-4 w-4" />
                                    تعديل البيانات
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Enhanced QR Code Modal */}
            {showQRModal && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                    <Card className="animate-scale-in w-full max-w-md">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="flex items-center gap-2">
                                    <QrCode className="h-5 w-5" />
                                    QR Code - {showQRModal.name}
                                </CardTitle>
                                <Button variant="ghost" size="icon" onClick={() => setShowQRModal(null)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4 text-center">
                            <div className="mx-auto flex h-48 w-48 items-center justify-center rounded-lg bg-gray-100">
                                <QrCode className="h-24 w-24 text-gray-400" />
                            </div>
                            <div>
                                <p className="font-medium">{showQRModal.name}</p>
                                <p className="text-sm text-gray-500">
                                    السنة {showQRModal.year} - {showQRModal.phone}
                                </p>
                            </div>
                            <p className="text-sm text-gray-600">امسح هذا الرمز لتسجيل الحضور</p>
                            <div className="flex gap-2">
                                <Button variant="outline" className="flex-1 bg-transparent">
                                    <Download className="mr-2 h-4 w-4" />
                                    تحميل
                                </Button>
                                <Button variant="outline" className="flex-1 bg-transparent">
                                    طباعة
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    );
}

UsersPage.layout = (page: React.ReactElement) => <AppLayout children={page} />;

export default UsersPage;
