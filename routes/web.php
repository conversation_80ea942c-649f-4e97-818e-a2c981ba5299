<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Attendance Management
    Route::get('attendance', function () {
        return Inertia::render('attendance');
    })->name('attendance');

    // User Management
    Route::get('add-user', function () {
        return Inertia::render('add-user');
    })->name('add-user');

    Route::get('users', function () {
        return Inertia::render('users', ['year' => 1]);
    })->name('users');

    Route::get('users/{year}', function ($year) {
        return Inertia::render('users', ['year' => (int)$year]);
    })->where('year', '[1-4]')->name('users.year');

    // Birthdays
    Route::get('birthdays', function () {
        return Inertia::render('birthdays');
    })->name('birthdays');

    // Reports
    Route::get('reports', function () {
        return Inertia::render('reports');
    })->name('reports');

    // App Settings (renamed to avoid conflict with settings.php)
    Route::get('app-settings', function () {
        return Inertia::render('app-settings');
    })->name('app-settings');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
